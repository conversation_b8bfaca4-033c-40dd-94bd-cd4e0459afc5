"use client";
import React, { createContext, useContext, useState, useCallback, ReactNode } from "react";

const DID_API_URL = "https://api.d-id.com";

interface Agent {
  id: string;
  preview_name: string;
  status: string;
  presenter: {
    type: string;
    voice: {
      type: string;
      voice_id: string;
    };
    thumbnail: string;
    source_url: string;
  };
  llm: {
    type: string;
    provider: string;
    model: string;
    instructions: string;
  };
}

interface ChatSession {
  id: string;
  agent_id: string;
  created: string;
  modified: string;
  messages: Array<{
    role: string;
    content: string;
    created_at: string;
  }>;
  owner_id: string;
}

interface StreamConnection {
  streamId: string;
  sessionId: string;
  offer: RTCSessionDescriptionInit;
  iceServers: Array<{
    urls: string[];
    username?: string;
    credential?: string;
  }>;
}

interface InterviewContextType {
  // D-ID Agent state
  agent: Agent | null;
  isCreatingAgent: boolean;
  agentError: string | null;
  createAgent: (instructions: string, agentName: string) => Promise<void>;

  // Chat Session state
  chatSession: ChatSession | null;
  isCreatingChat: boolean;
  chatError: string | null;
  createChatSession: () => Promise<void>;

  // Stream Connection state
  streamConnection: StreamConnection | null;
  isConnectingStream: boolean;
  streamError: string | null;
  createStreamConnection: () => Promise<void>;
  createStreamConnectionWithGreeting: (agentData: Agent) => Promise<void>;

  // Message sending
  isSendingMessage: boolean;
  messageError: string | null;
  sendMessage: (message: string) => Promise<void>;

  // Interview state
  currentQuestion: number;
  setCurrentQuestion: (question: number) => void;
  isInterviewStarted: boolean;
  setIsInterviewStarted: (started: boolean) => void;

  // Questions data
  questions: string[];
}

const InterviewContext = createContext<InterviewContextType | undefined>(undefined);

interface InterviewProviderProps {
  children: ReactNode;
}

export const InterviewProvider: React.FC<InterviewProviderProps> = ({ children }) => {
  // D-ID Agent state
  const [agent, setAgent] = useState<Agent | null>(null);
  const [isCreatingAgent, setIsCreatingAgent] = useState<boolean>(false);
  const [agentError, setAgentError] = useState<string | null>(null);

  // Chat Session state
  const [chatSession, setChatSession] = useState<ChatSession | null>(null);
  const [isCreatingChat, setIsCreatingChat] = useState<boolean>(false);
  const [chatError, setChatError] = useState<string | null>(null);

  // Stream Connection state
  const [streamConnection, setStreamConnection] = useState<StreamConnection | null>(null);
  const [isConnectingStream, setIsConnectingStream] = useState<boolean>(false);
  const [streamError, setStreamError] = useState<string | null>(null);

  // Message sending state
  const [isSendingMessage, setIsSendingMessage] = useState<boolean>(false);
  const [messageError, setMessageError] = useState<string | null>(null);

  // Interview state
  const [currentQuestion, setCurrentQuestion] = useState<number>(1);
  const [isInterviewStarted, setIsInterviewStarted] = useState<boolean>(false);
  
  // Questions data
  const questions = [
    "Tell us about yourself?",
    "What are your strengths?",
    "Why do you want this job?",
    "Where do you see yourself in 5 years?",
  ];

  const getAuthHeaders = () => {
    const apiKey = process.env.NEXT_PUBLIC_DID_API_KEY || process.env.DID_API_KEY || "";
    console.log("Using D-ID API Key:", apiKey ? `${apiKey.substring(0, 10)}...` : "NOT_FOUND");

    return {
      "Authorization": `Basic ${apiKey}`,
      "Content-Type": "application/json",
    };
  };

  // Helper function to retry API calls
  const retryApiCall = async (
    apiCall: () => Promise<Response>,
    maxRetries: number = 3,
    delay: number = 1000
  ): Promise<Response> => {
    let lastError: Error | null = null;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        const response = await apiCall();
        if (response.ok || response.status < 500) {
          // Return successful responses or client errors (don't retry client errors)
          return response;
        }
        throw new Error(`Server error: ${response.status} ${response.statusText}`);
      } catch (error) {
        lastError = error instanceof Error ? error : new Error(String(error));
        console.warn(`API call attempt ${attempt} failed:`, lastError.message);

        if (attempt < maxRetries) {
          console.log(`Retrying in ${delay}ms...`);
          await new Promise(resolve => setTimeout(resolve, delay));
          delay *= 2; // Exponential backoff
        }
      }
    }

    throw lastError || new Error("API call failed after all retries");
  };

  const createAgent = useCallback(async (instructions: string, agentName: string) => {
    // If agent already exists with same instructions, don't recreate
    if (agent && agent.llm.instructions === instructions && agent.preview_name === agentName) {
      return;
    }

    setIsCreatingAgent(true);
    setAgentError(null);

    const payload = {
      presenter: {
        type: "talk",
        voice: {
          type: "microsoft",
          voice_id: "en-US-JennyMultilingualV2Neural"
        },
        thumbnail: "https://create-images-results.d-id.com/DefaultPresenters/Zivva_f/thumbnail.jpeg",
        source_url: "https://create-images-results.d-id.com/DefaultPresenters/Zivva_f/thumbnail.jpeg"
      },
      llm: {
        type: "openai",
        provider: "openai",
        model: "gpt-4o-mini",
        instructions: instructions
      },
      preview_name: agentName
    };

    try {
      console.log("Creating D-ID Agent with payload:", payload);

      const response = await fetch(`${DID_API_URL}/agents`, {
        method: "POST",
        headers: getAuthHeaders(),
        body: JSON.stringify(payload),
      });

      console.log("D-ID Agent API Response Status:", response.status);

      if (!response.ok) {
        const errorText = await response.text();
        console.error("D-ID Agent API Error Response:", errorText);
        throw new Error(`Failed to create agent: ${response.status} ${response.statusText} - ${errorText}`);
      }

      const agentData: Agent = await response.json();
      console.log("D-ID Agent Created Successfully:", agentData);
      setAgent(agentData);

      // Automatically create stream connection after agent is created
      console.log("Agent created, now creating stream connection...");
      setTimeout(() => {
        if (!streamConnection && !isConnectingStream) {
          createStreamConnectionWithGreeting(agentData);
        }
      }, 1000); // Small delay to ensure state is updated
    } catch (err: unknown) {
      console.error("D-ID Agent Creation Error:", err);
      const errorMessage = err instanceof Error ? err.message : "Failed to create agent";
      setAgentError(`Agent Creation Failed: ${errorMessage}`);
    } finally {
      setIsCreatingAgent(false);
    }
  }, [agent]);

  const createChatSession = useCallback(async () => {
    if (!agent) {
      setChatError("No agent available to create chat session");
      return;
    }

    setIsCreatingChat(true);
    setChatError(null);

    try {
      console.log("Creating chat session for agent:", agent.id);

      const response = await fetch(`${DID_API_URL}/agents/${agent.id}/chat`, {
        method: "POST",
        headers: getAuthHeaders(),
        body: JSON.stringify({}), // Empty body as per documentation
      });

      console.log("Chat Session API Response Status:", response.status);

      if (!response.ok) {
        const errorText = await response.text();
        console.error("Chat Session API Error Response:", errorText);
        throw new Error(`Failed to create chat session: ${response.status} ${response.statusText} - ${errorText}`);
      }

      const chatData: ChatSession = await response.json();
      console.log("Chat Session Created Successfully:", chatData);
      setChatSession(chatData);
    } catch (err: unknown) {
      console.error("Chat Session Creation Error:", err);
      const errorMessage = err instanceof Error ? err.message : "Failed to create chat session";
      setChatError(`Chat Session Creation Failed: ${errorMessage}`);
    } finally {
      setIsCreatingChat(false);
    }
  }, [agent]);

  const createStreamConnection = useCallback(async () => {
    if (!agent) {
      setStreamError("No agent available to create stream connection");
      return;
    }

    setIsConnectingStream(true);
    setStreamError(null);

    try {
      console.log("Creating stream connection for agent:", agent.id);
      console.log("Using source URL:", agent.presenter.source_url);

      // Step 1: Create a new stream using talks/streams endpoint
      const streamPayload = {
        source_url: agent.presenter.source_url
      };

      console.log("Stream payload:", streamPayload);
      console.log("API headers:", getAuthHeaders());

      const streamResponse = await fetch(`${DID_API_URL}/talks/streams`, {
        method: "POST",
        headers: getAuthHeaders(),
        body: JSON.stringify(streamPayload),
      });

      console.log("Stream API Response Status:", streamResponse.status);

      if (!streamResponse.ok) {
        const errorText = await streamResponse.text();
        console.error("Stream API Error Response:", errorText);
        throw new Error(`Failed to create stream: ${streamResponse.status} ${streamResponse.statusText} - ${errorText}`);
      }

      const streamData = await streamResponse.json();
      console.log("Stream Created Successfully:", streamData);
      console.log("Stream offer:", streamData.offer);
      console.log("ICE servers:", streamData.ice_servers);

      const connectionData: StreamConnection = {
        streamId: streamData.id,
        sessionId: streamData.session_id,
        offer: streamData.offer,
        iceServers: streamData.ice_servers || []
      };

      setStreamConnection(connectionData);
    } catch (err: unknown) {
      console.error("Stream Connection Creation Error:", err);
      const errorMessage = err instanceof Error ? err.message : "Failed to create stream connection";
      setStreamError(`Stream Connection Failed: ${errorMessage}`);
    } finally {
      setIsConnectingStream(false);
    }
  }, [agent]);

  const createStreamConnectionWithGreeting = useCallback(async (agentData: Agent) => {
    setIsConnectingStream(true);
    setStreamError(null);

    try {
      console.log("Creating stream connection with greeting for agent:", agentData.id);
      console.log("Using source URL:", agentData.presenter.source_url);

      // Step 1: Create a new stream using talks/streams endpoint
      const streamPayload = {
        source_url: agentData.presenter.source_url
      };

      console.log("Stream payload:", streamPayload);

      const streamResponse = await fetch(`${DID_API_URL}/talks/streams`, {
        method: "POST",
        headers: getAuthHeaders(),
        body: JSON.stringify(streamPayload),
      });

      console.log("Stream API Response Status:", streamResponse.status);

      if (!streamResponse.ok) {
        const errorText = await streamResponse.text();
        console.error("Stream API Error Response:", errorText);
        throw new Error(`Failed to create stream: ${streamResponse.status} ${streamResponse.statusText} - ${errorText}`);
      }

      const streamData = await streamResponse.json();
      console.log("Stream Created Successfully:", streamData);
      console.log("Stream Data Keys:", Object.keys(streamData));
      console.log("Session ID from response:", streamData.session_id);
      console.log("Session ID type:", typeof streamData.session_id);

      // Check if session_id is in headers instead of body
      const sessionIdFromHeaders = streamResponse.headers.get('set-cookie');
      console.log("Session ID from headers:", sessionIdFromHeaders);

      // Use the session_id from the response body, or extract from headers if needed
      let sessionId = streamData.session_id;
      if (!sessionId || typeof sessionId !== 'string' || sessionId.includes('AWSALB')) {
        // If session_id is not valid, try to extract from response or use stream id
        sessionId = streamData.id; // Fallback to stream id
        console.warn("Using stream ID as session ID due to invalid session_id:", sessionId);
      }

      const connectionData: StreamConnection = {
        streamId: streamData.id,
        sessionId: sessionId,
        offer: streamData.offer,
        iceServers: streamData.ice_servers || []
      };

      setStreamConnection(connectionData);

      // Send greeting message after stream is established
      setTimeout(async () => {
        try {
          const greetingMessage = `Hello! I'm ${agentData.preview_name}, your AI interview assistant. Welcome to your interview session. I'll be conducting your interview today. Please make yourself comfortable, and when you're ready, we can begin with the first question.`;

          console.log("Sending greeting message:", greetingMessage);

          const greetingResponse = await fetch(`${DID_API_URL}/talks/streams/${streamData.id}`, {
            method: "POST",
            headers: getAuthHeaders(),
            body: JSON.stringify({
              script: {
                type: "text",
                input: greetingMessage
              },
              config: {
                stitch: true,
              },
              session_id: streamData.session_id
            }),
          });

          if (greetingResponse.ok) {
            console.log("Greeting message sent successfully");
          } else {
            const errorText = await greetingResponse.text();
            console.error("Failed to send greeting message:", greetingResponse.status, errorText);
            console.error("Greeting Request URL:", `${DID_API_URL}/talks/streams/${streamData.id}`);
            console.error("Greeting Request Headers:", getAuthHeaders());
            console.error("Greeting Request Body:", JSON.stringify({
              script: {
                type: "text",
                input: greetingMessage
              },
              config: {
                stitch: true,
              },
              session_id: streamData.session_id
            }));
          }
        } catch (error) {
          console.error("Error sending greeting message:", error);
        }
      }, 3000); // Wait 3 seconds for WebRTC connection to establish

    } catch (err: unknown) {
      console.error("Stream Connection Creation Error:", err);
      const errorMessage = err instanceof Error ? err.message : "Failed to create stream connection";
      setStreamError(`Stream Connection Failed: ${errorMessage}`);
    } finally {
      setIsConnectingStream(false);
    }
  }, []);

  const sendMessage = useCallback(async (message: string) => {
    if (!streamConnection) {
      setMessageError("Stream connection not available");
      return;
    }

    setIsSendingMessage(true);
    setMessageError(null);

    try {
      console.log("Sending message to D-ID stream:", message);

      // Send the message to the D-ID stream to make the avatar speak with retry logic
      const response = await retryApiCall(() =>
        fetch(`${DID_API_URL}/talks/streams/${streamConnection.streamId}`, {
          method: "POST",
          headers: getAuthHeaders(),
          body: JSON.stringify({
            script: {
              type: "text",
              input: message
            },
            config: {
              stitch: true,
            },
            session_id: streamConnection.sessionId
          }),
        })
      );

      console.log("Send Message API Response Status:", response.status);

      if (!response.ok) {
        const errorText = await response.text();
        console.error("Send Message API Error Response:", errorText);

        // Log more details for debugging
        console.error("Request URL:", `${DID_API_URL}/talks/streams/${streamConnection.streamId}`);
        console.error("Request Headers:", getAuthHeaders());
        console.error("Request Body:", JSON.stringify({
          script: {
            type: "text",
            input: message
          },
          config: {
            stitch: true,
          },
          session_id: streamConnection.sessionId
        }));

        throw new Error(`Failed to send message: ${response.status} ${response.statusText} - ${errorText}`);
      }

      console.log("Message sent successfully to D-ID stream");
      // The video response will be streamed via WebRTC connection
    } catch (err: unknown) {
      console.error("Send Message Error:", err);
      const errorMessage = err instanceof Error ? err.message : "Failed to send message";
      setMessageError(`Send Message Failed: ${errorMessage}`);
    } finally {
      setIsSendingMessage(false);
    }
  }, [streamConnection]);

  const value: InterviewContextType = {
    // D-ID Agent state
    agent,
    isCreatingAgent,
    agentError,
    createAgent,

    // Chat Session state
    chatSession,
    isCreatingChat,
    chatError,
    createChatSession,

    // Stream Connection state
    streamConnection,
    isConnectingStream,
    streamError,
    createStreamConnection,
    createStreamConnectionWithGreeting,

    // Message sending
    isSendingMessage,
    messageError,
    sendMessage,

    // Interview state
    currentQuestion,
    setCurrentQuestion,
    isInterviewStarted,
    setIsInterviewStarted,

    // Questions data
    questions,
  };

  return (
    <InterviewContext.Provider value={value}>
      {children}
    </InterviewContext.Provider>
  );
};

export const useInterview = (): InterviewContextType => {
  const context = useContext(InterviewContext);
  if (context === undefined) {
    throw new Error('useInterview must be used within an InterviewProvider');
  }
  return context;
};
