"use client";
import React, { useRef, useEffect, useState, useCallback } from "react";
import { useInterview } from "@/context/InterviewContext";

interface WebRTCStreamProps {
  className?: string;
  onConnectionStateChange?: (state: RTCPeerConnectionState) => void;
  onVideoReady?: () => void;
  onVideoEnd?: () => void;
}

const WebRTCStream: React.FC<WebRTCStreamProps> = ({
  className = "",
  onConnectionStateChange,
  onVideoReady,
  onVideoEnd,
}) => {
  const videoRef = useRef<HTMLVideoElement>(null);
  const peerConnectionRef = useRef<RTCPeerConnection | null>(null);
  const [connectionState, setConnectionState] = useState<RTCPeerConnectionState>("new");
  const [isVideoPlaying, setIsVideoPlaying] = useState(false);

  const { streamConnection, streamError } = useInterview();

  // Debug logging
  useEffect(() => {
    console.log("WebRTCStream: streamConnection changed:", streamConnection);
    console.log("WebRTCStream: streamError:", streamError);
  }, [streamConnection, streamError]);

  const setupPeerConnection = useCallback(async () => {
    if (!streamConnection) {
      console.log("No stream connection available");
      return;
    }

    try {
      console.log("Setting up WebRTC peer connection with D-ID...");

      // Create peer connection with ICE servers from D-ID
      const configuration: RTCConfiguration = {
        iceServers: streamConnection.iceServers.length > 0
          ? streamConnection.iceServers
          : [{ urls: "stun:stun.l.google.com:19302" }] // Fallback STUN server
      };

      const peerConnection = new RTCPeerConnection(configuration);
      peerConnectionRef.current = peerConnection;

      // Handle connection state changes
      peerConnection.onconnectionstatechange = () => {
        const state = peerConnection.connectionState;
        console.log("WebRTC connection state:", state);
        setConnectionState(state);
        onConnectionStateChange?.(state);
      };

      // Handle ICE connection state changes
      peerConnection.oniceconnectionstatechange = () => {
        console.log("ICE connection state:", peerConnection.iceConnectionState);
        if (peerConnection.iceConnectionState === 'connected' || peerConnection.iceConnectionState === 'completed') {
          console.log("WebRTC connection established successfully - ready for streaming");
          setConnectionState('connected');
        } else if (peerConnection.iceConnectionState === 'failed') {
          console.error("WebRTC connection failed");
          setConnectionState('failed');
        }
      };

      // Handle incoming media stream
      peerConnection.ontrack = (event) => {
        console.log("Received remote stream:", event.streams[0]);
        if (videoRef.current && event.streams[0]) {
          videoRef.current.srcObject = event.streams[0];
          videoRef.current.play().then(() => {
            console.log("Video started playing");
            setIsVideoPlaying(true);
            onVideoReady?.();
          }).catch((error) => {
            console.error("Error playing video:", error);
          });
        }
      };

      // Handle ICE candidates - send them to D-ID
      peerConnection.onicecandidate = async (event) => {
        if (event.candidate) {
          console.log("Sending ICE candidate to D-ID:", event.candidate);
          const { candidate, sdpMid, sdpMLineIndex } = event.candidate;

          try {
            const response = await fetch(`https://api.d-id.com/talks/streams/${streamConnection.streamId}/ice`, {
              method: 'POST',
              headers: {
                'Authorization': `Basic ${process.env.NEXT_PUBLIC_DID_API_KEY || process.env.DID_API_KEY || ""}`,
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({
                candidate,
                sdpMid,
                sdpMLineIndex,
                session_id: streamConnection.sessionId
              }),
            });

            if (!response.ok) {
              console.error("Failed to send ICE candidate:", response.status);
            }
          } catch (error) {
            console.error("Error sending ICE candidate:", error);
          }
        }
      };

      // Step 2: Set the remote description (offer from D-ID) and create answer
      if (streamConnection.offer) {
        console.log("Setting remote description with D-ID offer");
        await peerConnection.setRemoteDescription(streamConnection.offer);

        console.log("Creating answer for D-ID");
        const answer = await peerConnection.createAnswer();
        await peerConnection.setLocalDescription(answer);

        console.log("Sending answer to D-ID");
        // Send the answer back to D-ID
        const response = await fetch(`https://api.d-id.com/talks/streams/${streamConnection.streamId}/sdp`, {
          method: 'POST',
          headers: {
            'Authorization': `Basic ${process.env.NEXT_PUBLIC_DID_API_KEY || process.env.DID_API_KEY || ""}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            answer: answer,
            session_id: streamConnection.sessionId
          }),
        });

        if (!response.ok) {
          throw new Error(`Failed to send SDP answer: ${response.status}`);
        }

        console.log("SDP answer sent successfully to D-ID");
      }

      console.log("WebRTC peer connection setup completed");

    } catch (error) {
      console.error("Error setting up peer connection:", error);
    }
  }, [streamConnection, onConnectionStateChange, onVideoReady]);

  const cleanup = useCallback(() => {
    if (peerConnectionRef.current) {
      peerConnectionRef.current.close();
      peerConnectionRef.current = null;
    }
    if (videoRef.current) {
      videoRef.current.srcObject = null;
    }
    setIsVideoPlaying(false);
    setConnectionState("new");
  }, []);

  useEffect(() => {
    if (streamConnection) {
      setupPeerConnection();
    }

    return cleanup;
  }, [streamConnection, setupPeerConnection, cleanup]);

  const handleVideoEnded = () => {
    console.log("Video ended");
    setIsVideoPlaying(false);
    onVideoEnd?.();
  };

  const handleVideoError = (error: React.SyntheticEvent<HTMLVideoElement, Event>) => {
    console.error("Video error:", error);
    setIsVideoPlaying(false);
  };

  if (streamError) {
    return (
      <div className={`flex items-center justify-center bg-gray-100 rounded-lg ${className}`}>
        <div className="text-center p-4">
          <p className="text-red-600 text-sm">Stream Error:</p>
          <p className="text-red-500 text-xs mt-1">{streamError}</p>
        </div>
      </div>
    );
  }

  if (!streamConnection) {
    return (
      <div className={`flex items-center justify-center bg-gray-100 rounded-lg ${className}`}>
        <div className="text-center p-4">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2"></div>
          <p className="text-gray-600 text-sm">Initializing stream...</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`relative bg-black rounded-lg overflow-hidden ${className}`}>
      <video
        ref={videoRef}
        className="w-full h-full object-cover"
        autoPlay
        playsInline
        muted={false}
        onEnded={handleVideoEnded}
        onError={handleVideoError}
      />
      
      {/* Connection status indicator */}
      <div className="absolute top-2 right-2">
        <div className={`w-3 h-3 rounded-full ${
          connectionState === "connected" ? "bg-green-500" :
          connectionState === "connecting" ? "bg-yellow-500" :
          connectionState === "failed" || connectionState === "disconnected" ? "bg-red-500" :
          "bg-gray-500"
        }`} title={`Connection: ${connectionState}`} />
      </div>

      {/* Loading overlay */}
      {!isVideoPlaying && connectionState !== "failed" && (
        <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-50">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-white mx-auto mb-4"></div>
            <p className="text-white text-sm">
              {connectionState === "connecting" ? "Connecting..." : "Loading video..."}
            </p>
          </div>
        </div>
      )}
    </div>
  );
};

export default WebRTCStream;
